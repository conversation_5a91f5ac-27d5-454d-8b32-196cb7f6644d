﻿@model Lab03.Models.Category

@{
    ViewData["Title"] = "Chi tiết thể loại";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4">📁 Chi tiết thể loại</h2>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm border-0">
            <div class="card-body">
                <h5 class="card-title">Tên thể loại</h5>
                <p class="card-text fs-5">@Model.Name</p>

                <div class="mt-4">
                    <a class="btn btn-primary" asp-action="Update" asp-route-id="@Model.Id">✏️ Sửa</a>
                    <a class="btn btn-secondary" asp-action="Index">⬅️ Quay lại danh sách</a>
                </div>
            </div>
        </div>
    </div>
</div>

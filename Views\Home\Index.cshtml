﻿@model IEnumerable<Lab03.Models.Book>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Index</h1>

<div class="row pt-3">
    @foreach (var item in Model)
    {
            <div class="col-lg-3 col-md-6 d-flex mb-4"> <!-- Thêm mb-4 -->
                <div class="card h-100 w-100 border-primary" style="border-radius: 5px">
                    <img src="@item.Cover" class="card-img-top" style="height: 300px; object-fit: cover;" />

                    <div class="card-body d-flex flex-column">
                        <div class="p-1">
                            <p class="card-title h5 text-primary">@item.Title</p>
                            <p class="card-title text-secondary">@item.Author</p>
                        </div>

                        <div class="p-1">
                            <p>Price: <strike>@item.Price.ToString("0.00")</strike></p>
                        </div>

                        <div class="mt-auto">
                            <a asp-controller="Home" asp-action="Detail" asp-route-id="@item.ID" 
                               class="btn btn-primary form-control">DETAIL</a>
                        </div>
                    </div>
                </div>
            </div>
    }
</div>

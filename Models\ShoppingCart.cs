﻿using System.ComponentModel.DataAnnotations;

namespace Lab03.Models
{
    public class ShoppingCart
    {
        public Book book { get; set; }
        [Range(0, 10000)]
        public int Quantity { get; set; }
        public List<CartItem> Items { get; set; } = new List<CartItem>();

        public void AddItem(CartItem item)
        {
            var existingItem = Items.FirstOrDefault(i => i.BookId == item.BookId);
            if (existingItem != null)
            {
                existingItem.Quantity += item.Quantity;
            }
            else
            {
                Items.Add(item);
            }
        }

        public void RemoveItem(int bookId)
        {
            Items.RemoveAll(i => i.BookId == bookId);
        }
    }
}

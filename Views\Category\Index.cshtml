﻿@model IEnumerable<Lab03.Models.Category>

@{
    ViewData["Title"] = "Danh sách thể loại";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <a class="btn btn-primary me-2" asp-controller="Category" asp-action="Add">➕ Thêm thể loại</a>
            <a class="btn btn-outline-secondary" asp-controller="Book" asp-action="Index">📚 Xem sách</a>
        </div>
    </div>

    <table class="table table-bordered table-hover">
        <thead class="table-dark">
            <tr>
                <th>Mã thể loại</th>
                <th>Tên thể loại</th>
                <th>Hành động</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>
                        <a class="btn btn-sm btn-warning" asp-action="Update" asp-route-id="@item.Id">Sửa</a>
                        <a class="btn btn-sm btn-info text-white" asp-action="Detail" asp-route-id="@item.Id">Chi tiết</a>
                        <a class="btn btn-sm btn-danger" asp-action="Delete" asp-route-id="@item.Id">Xoá</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>
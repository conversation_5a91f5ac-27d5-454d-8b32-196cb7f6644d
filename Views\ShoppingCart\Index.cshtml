﻿@model Lab03.Models.ShoppingCart
@{
    ViewData["Title"] = "Giỏ hàng của bạn";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <h2 class="text-primary fw-bold text-center mb-4">
        <i class="bi bi-cart4"></i> Giỏ hàng của bạn
    </h2>

    <table class="table table-bordered table-hover shadow">
        <thead class="table-dark text-center">
            <tr>
                <th>Sách</th>
                <th>Tác giả</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Tổng</th>
                <th>Thao tác</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.Items)
            {
                <tr class="align-middle text-center">
                    <td>@item.Title</td>
                    <td>@item.Book.Author</td>
                    <td>@item.Book.Price.ToString("c")</td>
                    <td>
                        <form asp-action="UpdateQuantity" method="post" class="d-inline">
                            <input type="hidden" name="bookId" value="@item.BookId" />
                            <input type="number" name="quantity" value="@item.Quantity" min="1" class="form-control d-inline w-50" />
                            <button type="submit" class="btn btn-sm btn-warning ms-2">Cập nhật</button>
                        </form>
                    </td>
                    <td>@item.Price.ToString("c")</td>
                    <td>
                        <a asp-controller="ShoppingCart" asp-action="RemoveItem" asp-route-id="@item.BookId"
                           class="btn btn-sm btn-danger" onclick="return confirm('Bạn chắc chắn muốn xoá?')">
                            <i class="bi bi-trash"></i> Xoá
                        </a>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <div class="d-flex justify-content-between mt-4">
        <a asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left-circle"></i> Tiếp tục mua sách
        </a>
        <a asp-controller="ShoppingCart" asp-action="Checkout" class="btn btn-success">
            <i class="bi bi-credit-card"></i> Thanh toán
        </a>
    </div>
</div>

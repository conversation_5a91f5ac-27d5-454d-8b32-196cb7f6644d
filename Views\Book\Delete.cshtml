﻿@model Lab03.Models.Book

@{
    ViewData["Title"] = "Xoá sách";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4 text-danger">❌ <PERSON><PERSON><PERSON> nhận xoá sách</h2>

<div class="alert alert-warning">
    Bạn có chắc chắn muốn xoá sách <strong>@Model.Title</strong> này không? Hành động này không thể hoàn tác.
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm border-0">
            <div class="row g-0">
                <div class="col-md-4 p-3 text-center">
                    @if (!string.IsNullOrEmpty(Model.Cover))
                    {
                        <img src="@Url.Content("~/" + Model.Cover)" alt="Ảnh sách" class="img-fluid rounded shadow-sm" />
                    }
                    else
                    {
                        <span class="text-muted fst-italic">Không có ảnh</span>
                    }
                </div>
                <div class="col-md-8">
                    <div class="card-body">
                        <h5 class="card-title">@Model.Title</h5>
                        <p class="card-text"><strong>Tác giả:</strong> @Model.Author</p>
                        <p class="card-text"><strong>Năm xuất bản:</strong> @Model.PublishYear</p>
                        <p class="card-text"><strong>Giá:</strong> @($"{Model.Price:#,##0} ₫")</p>
                        <p class="card-text"><strong>Thể loại:</strong> @Model.Category?.Name</p>

                        <form asp-action="Delete" method="post" class="mt-4">
                            <input type="hidden" asp-for="ID" />
                            <button type="submit" class="btn btn-danger me-2">🗑 Xoá</button>
                            <a class="btn btn-secondary" asp-action="Index">⬅️ Huỷ</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

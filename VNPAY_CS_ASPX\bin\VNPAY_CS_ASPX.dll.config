﻿<?xml version="1.0"?>
<configuration>
  <appSettings>
    <!--VNPAY SETTINGS-->
    <add key="vnp_Url" value="https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"/>
    <add key="vnp_Api" value="https://sandbox.vnpayment.vn/merchant_webapi/api/transaction"/>
    <add key="vnp_TmnCode" value="" />
    <add key="vnp_HashSecret" value="" /> 
    <add key="vnp_Returnurl" value="http://localhost:16262/vnpay_return.aspx"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.1" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.6.1"/>
    <!--
            The <authentication> section enables configuration 
            of the security authentication mode used by 
            ASP.NET to identify an incoming user. 
        -->
    <authentication mode="Windows"/>
    <!--
            The <customErrors> section enables configuration 
            of what to do if/when an unhandled error occurs 
            during the execution of a request. Specifically, 
            it enables developers to configure html error pages 
            to be displayed in place of a error stack trace.

        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID"/>
  </system.web>
</configuration>
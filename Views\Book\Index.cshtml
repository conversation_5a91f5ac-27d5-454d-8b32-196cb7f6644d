﻿@model IEnumerable<Lab03.Models.Book>

@{
    ViewData["Title"] = "Book List";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1 class="mb-4">📚 <PERSON>h sách</h1>

<div class="text-end mb-3">
    <a class="btn btn-success" asp-controller="Book" asp-action="Add">
        <i class="bi bi-plus-circle"></i> Thêm sách mới
    </a>
</div>

<table class="table table-hover table-bordered align-middle shadow-sm">
    <thead class="table-dark">
        <tr>
            <th>Tiêu đề</th>
            <th>Tác giả</th>
            <th>Năm XB</th>
            <th>Giá</th>
            <th>Ảnh bìa</th>
            <th>Thể loại</th>
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Title</td>
                <td>@item.Author</td>
                <td>@item.PublishYear</td>
                <td>@($"{item.Price:#,##0} ₫")</td>
                <td>
                    @if (!string.IsNullOrEmpty(item.Cover))
                    {
                        <img src="@Url.Content("~/" + item.Cover)" alt="Ảnh sách" class="img-thumbnail" style="width: 80px;" />
                    }
                    else
                    {
                        <span class="text-muted">Không có ảnh</span>
                    }
                </td>
                <td>@item.Category?.Name</td>
                <td class="text-center">
                    <a class="btn btn-sm btn-primary bi bi-view-list" asp-action="Update" asp-route-id="@item.ID">Sửa</a>
                    <a class="btn btn-sm bi bi-pencil" asp-action="Detail" asp-route-id="@item.ID">Chi tiết</a>
                    <a class="btn btn-sm bi bi-trash" asp-action="Delete" asp-route-id="@item.ID">Xoá</a>
                </td>
            </tr>
        }
    </tbody>
</table>

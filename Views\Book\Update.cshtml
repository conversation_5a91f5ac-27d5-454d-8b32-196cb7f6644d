﻿@model Lab03.Models.Book

@{
    ViewData["Title"] = "Cập nhật sách";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4">✏️ Cập nhật thông tin sách</h2>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm border-0">
            <div class="card-body">
                <form asp-action="Update">
                    <input type="hidden" asp-for="ID" />

                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                    <div class="mb-3">
                        <label asp-for="Title" class="form-label"></label>
                        <input asp-for="Title" class="form-control" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Author" class="form-label"></label>
                        <input asp-for="Author" class="form-control" />
                        <span asp-validation-for="Author" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="PublishYear" class="form-label"></label>
                        <input asp-for="PublishYear" class="form-control" />
                        <span asp-validation-for="PublishYear" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Price" class="form-label"></label>
                        <input asp-for="Price" class="form-control" />
                        <span asp-validation-for="Price" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Cover" class="form-label"></label>
                        <input asp-for="Cover" class="form-control" />
                        <span asp-validation-for="Cover" class="text-danger"></span>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Cover))
                    {
                        <div class="mb-3">
                            <label>Ảnh hiện tại:</label><br />
                            <img src="@Url.Content("~/" + Model.Cover)" alt="Ảnh sách" class="img-thumbnail" style="max-width: 200px;" />
                        </div>
                    }

                    <div class="mb-3">
                        <label asp-for="CategoryId" class="form-label">Thể loại</label>
                        <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.CategoryId">
                            <option value="">-- Chọn thể loại --</option>
                        </select>
                        <span asp-validation-for="CategoryId" class="text-danger"></span>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">💾 Lưu thay đổi</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-3">
            <a class="btn btn-secondary" asp-action="Index">⬅️ Quay lại danh sách</a>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

﻿@model Lab03.Models.Category

@{
    ViewData["Title"] = "Thêm thể loại";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4">📁 Thêm thể loại mới</h2>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm border-0">
            <div class="card-body">
                <form asp-action="Add" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                    <div class="mb-3">
                        <label asp-for="Name" class="form-label">Tên thể loại</label>
                        <input asp-for="Name" class="form-control" placeholder="VD: Khoa học, Văn học..." />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">💾 Lưu</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-3">
            <a class="btn btn-secondary" asp-action="Index">⬅️ Trở về danh sách</a>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

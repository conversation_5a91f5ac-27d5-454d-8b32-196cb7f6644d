﻿@model Lab03.Models.Order
@{
    ViewData["Title"] = "Thanh toán";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5 mb-5">
    <h2 class="text-center text-primary fw-bold mb-4">
        <i class="bi bi-credit-card-fill me-2"></i> BILLING ADDRESS
    </h2>

    <form asp-action="Checkout" method="post" class="shadow p-4 bg-white rounded">
        <div class="form-group mb-3">
            <label>Email (Optional)</label>
            <input asp-for="User.Email" class="form-control" placeholder="<EMAIL>" />
        </div>

        <div class="form-group mb-3">
            <label>Address</label>
            <input asp-for="ShippingAddress" class="form-control" placeholder="Địa chỉ giao hàng" />
        </div>

        <div class="form-group mb-3">
            <label>Notes</label>
            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="Ghi chú..."></textarea>
        </div>

        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" id="sameAddress" />
            <label class="form-check-label" for="sameAddress">
                Shipping address is the same as my billing address
            </label>
        </div>

        <div class="form-check mb-4">
            <input class="form-check-input" type="checkbox" id="saveInfo" />
            <label class="form-check-label" for="saveInfo">
                Save this information for next time
            </label>
        </div>

        <hr />
        <h4 class="fw-bold mb-3">PAYMENT</h4>
        <div class="form-check mb-4">
            <input class="form-check-input" type="radio" checked />
            <label class="form-check-label">
                Ship COD
            </label>
        </div>

        <button type="submit" class="btn btn-primary w-100 py-2 fw-bold" style="background: linear-gradient(to right, #6a11cb, #2575fc);">
            CONTINUE TO CHECKOUT
        </button>
    </form>
</div>

{"Files": [{"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Lab03.bundle.scp.css", "PackagePath": "staticwebassets\\Lab03.ij2ghiu7cq.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\css\\Bootswach.css", "PackagePath": "staticwebassets\\css\\Bootswach.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\Doraemon.jpg", "PackagePath": "staticwebassets\\images\\Doraemon.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\cover1.jpg", "PackagePath": "staticwebassets\\images\\cover1.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\dacnhantam.jpg", "PackagePath": "staticwebassets\\images\\dacnhantam.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\haisophan.jpg", "PackagePath": "staticwebassets\\images\\haisophan.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\hinh1.jpg", "PackagePath": "staticwebassets\\images\\hinh1.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\hinh2.jpg", "PackagePath": "staticwebassets\\images\\hinh2.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\hinh3.jpg", "PackagePath": "staticwebassets\\images\\hinh3.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\nggiauco.jpg", "PackagePath": "staticwebassets\\images\\nggiauco.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\nhagiakim.jpg", "PackagePath": "staticwebassets\\images\\nhagiakim.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\images\\zzz.jpg", "PackagePath": "staticwebassets\\images\\zzz.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\Lab03\\Lab03\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Lab03.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Lab03.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.Lab03.props", "PackagePath": "build\\Lab03.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.Lab03.props", "PackagePath": "buildMultiTargeting\\Lab03.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.Lab03.props", "PackagePath": "buildTransitive\\Lab03.props"}], "ElementsToRemove": []}
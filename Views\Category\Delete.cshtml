﻿@model Lab03.Models.Category

@{
    ViewData["Title"] = "Xoá thể loại";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4 text-danger">❌ <PERSON><PERSON><PERSON> nhận xoá thể loại</h2>

<div class="alert alert-warning">
    Bạn có chắc chắn muốn xoá thể loại <strong>@Model.Name</strong> không? Hành động này sẽ không thể hoàn tác.
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm border-0">
            <div class="card-body">
                <h5 class="card-title">Tên thể loại</h5>
                <p class="card-text fs-5">@Model.Name</p>

                <form asp-action="Delete" method="post" class="mt-4">
                    <input type="hidden" asp-for="Id" />
                    <button type="submit" class="btn btn-danger me-2">🗑 Xoá</button>
                    <a class="btn btn-secondary" asp-action="Index">⬅️ Huỷ</a>
                </form>
            </div>
        </div>
    </div>
</div>

﻿@model Lab03.Models.Category

@{
    ViewData["Title"] = "Cập nhật thể loại";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4">✏️ Cập nhật thể loại</h2>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm border-0">
            <div class="card-body">
                <form asp-action="Update" method="post">
                    <input type="hidden" asp-for="Id" />

                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                    <div class="mb-3">
                        <label asp-for="Name" class="form-label">Tên thể loại</label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">💾 <PERSON><PERSON><PERSON> thay đổi</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-3">
            <a class="btn btn-secondary" asp-action="Index">⬅️ Quay lại danh sách</a>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

﻿@model ShoppingCart

@{
    ViewData["Title"] = "Detail";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Detail</h1>

<form method="post">
    <div class="card container">
        <div class="card-header bg-primary text-light h-lg-0 row">
            <div class="col-12 col-md-6">
                <h1 class="text-white-50">@Model.book.Title</h1>
                <h3 class="text-warning">@Model.book.Author</h3>
                <div class="col-12 col-md-6 text-end pt-4">
                    <span class="badge bg-info p-2" style="height:30px;">
                        @Model.book.CategoryId
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body row container">
            <div class="container rounded p-2">
                <div class="row">
                    <div class="col-lg-8 col-12">
                        <h5 class="text-muted">Author: @Model.book.Author</h5>
                        <div class="row pl-2">
                            <h5 class="text-muted pl-2">
                                Price: <strike>@Model.book.Price.ToString("c")</strike>
                            </h5>
                        </div>

                        <div class="row text-center pl-2">
                            <div class="col-1 col-2 bg-secondary border-bottom"></div>
                            <div class="col-2 bg-secondary border-bottom">Quantity</div>
                            <div class="col-1 col-2 bg-secondary border-bottom">100</div>
                            <div class="col-1 col-2 bg-secondary border-bottom">100</div>
                        </div>

                        <div class="row text-center pl-2" style="color: maroon; font-weight: bold;">
                            <div class="col-1 col-2 bg-secondary"></div>
                            <div class="col-2 bg-secondary"></div>
                            <div class="col-1 col-2 bg-secondary">@Model.book.Price</div>
                            <div class="col-1 col-2 bg-secondary">@Model.book.Price</div>
                        </div>
                        <div class="row pl-2 pt-2">
                            <div class="col-12">
                                <p class="text-primary">📄 Description</p>
                                <div class="border rounded p-3" style="text-align: justify;">
                                    @Html.Raw(Model.book.Description)
                                </div>
                            </div>
                        </div>

                        <div class="row pl-2 pt-2">
                            <div class="col-12">
                                <div class="row pl-2">
                                    <div class="col-12">
                                        <h6>📦 Count</h6>
                                        <div class="col-lg-3">
                                            <input asp-for="Quantity" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-lg-3 offset-lg-1 text-center">
                        <img src="" width="100%" class="rounded" />
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <div class="col-12 col-md-6 pb-1">
                <a class="btn btn-success form-control" asp-action="Index" style="height:50px;">⬅️ Back to List</a>
            </div>
            <div class="col-12 col-md-6 pb-1">
                <button type="submit" value="Add to cart" class="transition btn btn-outline-success" style="height:50px"
                        asp-route-id="@Model.book.ID"
                        asp-controller="ShoppingCart"
                        asp-action="AddToCart"
                        asp-route-quantity="@Model.Quantity">
                    <i class="bi bi-basket"> </i>
                    Add to cart
                </button>
            </div>
        </div>
    </div>
</form>

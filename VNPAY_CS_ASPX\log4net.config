<?xml version="1.0" encoding="utf-8" ?>
<log4net debug="false" info="true">
  <root>
    <level value="DEBUG" />
    <appender-ref ref="RollingLogFileAppender"/>
  </root>
  <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="c:\Logs\vnpaydemo\payment.log" />
    <appendToFile value="true" />
    <rollingStyle value="5"/>
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="10MB" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%d %-18.18M  -%message %-22.22%newline" />
    </layout>
  </appender>


</log4net>